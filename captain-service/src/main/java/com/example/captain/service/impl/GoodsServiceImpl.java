package com.example.captain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.captain.config.AmazonSiteConfig;
import com.example.captain.dto.GoodsCreateRequest;
import com.example.captain.dto.GoodsDTO;
import com.example.captain.dto.GoodsQueryParam;
import com.example.captain.entity.Goods;
import com.example.captain.entity.Product;
import com.example.captain.entity.Store;
import com.example.captain.entity.Supply;
import com.example.captain.enums.AmazonSite;
import com.example.captain.enums.SiteRegion;
import com.example.captain.mapper.GoodsMapper;
import com.example.captain.mapper.ProductMapper;
import com.example.captain.mapper.SupplyMapper;
import com.example.captain.service.GoodsService;
import com.example.captain.service.ProductService;
import com.example.captain.service.StoreService;
import com.example.captain.service.SupplyService;
import com.example.captain.util.AsinCompressor;
import com.example.captain.util.EanCodeUtil;
import com.example.common.exception.BusinessException;
import com.example.common.model.ErrorCodeEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Amazon待发布商品服务实现类
 */
@Service
@Slf4j
public class GoodsServiceImpl implements GoodsService {

    private final GoodsMapper goodsMapper;
    private final ProductMapper productMapper;
    private final SupplyMapper supplyMapper;
    private final ProductService productService;
    private final SupplyService supplyService;
    private final StoreService storeService;
    private final AmazonSiteConfig amazonSiteConfig;
    private final ObjectMapper objectMapper;

    @Autowired
    public GoodsServiceImpl(GoodsMapper goodsMapper,
                           ProductMapper productMapper,
                           SupplyMapper supplyMapper,
                           ProductService productService,
                           SupplyService supplyService,
                           StoreService storeService,
                           AmazonSiteConfig amazonSiteConfig,
                           ObjectMapper objectMapper) {
        this.goodsMapper = goodsMapper;
        this.productMapper = productMapper;
        this.supplyMapper = supplyMapper;
        this.productService = productService;
        this.supplyService = supplyService;
        this.storeService = storeService;
        this.amazonSiteConfig = amazonSiteConfig;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GoodsDTO> createGoods(GoodsCreateRequest request) {
        log.info("开始创建Amazon商品信息: eventId={}, amazonAsin={}, supplyObjectId={}, storeId={}, amazonSite={}",
                request.getEventId(), request.getAmazonAsin(), request.getSupplyObjectId(),
                request.getStoreId(), request.getAmazonSite());

        List<GoodsDTO> resultList = new ArrayList<>();

        // 1. 获取Amazon商品信息
        Product product = getProductByAsin(request.getAmazonAsin());
        if (product == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND,
                    "未找到ASIN为 " + request.getAmazonAsin() + " 的Amazon商品");
        }

        // 2. 获取1688货源信息
        Supply supply = getSupplyByObjectIdAndAsin(request.getSupplyObjectId(), request.getAmazonAsin());
        if (supply == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND,
                    "未找到object_id为 " + request.getSupplyObjectId() + " 且amazon_asin为 " + request.getAmazonAsin() + " 的1688货源");
        }

        // 3. 获取店铺信息
        Store store = getStoreById(request.getStoreId());
        if (store == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND,
                    "未找到ID为 " + request.getStoreId() + " 的店铺");
        }

        // 4. 验证数据关联性
        if (!request.getAmazonAsin().equals(supply.getAmazonAsin())) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,
                    "Amazon商品ASIN与1688货源关联的ASIN不匹配");
        }

        // 5. 检查是否已存在相同的商品记录
        Goods existingGoods = goodsMapper.findByEventIdAndAmazonAsin(request.getEventId(), request.getAmazonAsin());
        if (existingGoods != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,
                    "该事件下已存在相同ASIN的商品记录");
        }

        // 6. 创建主商品数据
        Goods mainGoods = createGoodsFromProductAndSupply(request.getEventId(), product, supply, store, request.getAmazonSite(), false);

        List<Product> childProducts = null;
        // 7. 如果是父变体商品，创建子商品
        if (product.getIsParentVariant() != null && product.getIsParentVariant() &&
            product.getIsMainProduct() != null && product.getIsMainProduct()) {

            log.info("检测到父变体商品，开始创建子商品: parentAsin={}", product.getAsin());

            // 根据父ASIN查询子商品列表
            childProducts = productMapper.findChildProductsByParentAsin(product.getAsin());

            // 设置父级主商品的总库存信息
            mainGoods.setQuantity(20 * childProducts.size()); // 子商品默认库存20 * 子商品数量
        }

        goodsMapper.insert(mainGoods);
        resultList.add(convertToDTO(mainGoods));

        log.info("成功创建主商品: id={}, itemSku={}", mainGoods.getId(), mainGoods.getItemSku());

        if (childProducts !=null){
            for (Product childProduct : childProducts) {
                try {
                    // 检查子商品是否已存在（添加parent_child='child'约束条件）
                    Goods existingChildGoods = goodsMapper.findChildGoodsByEventIdAndAmazonAsin(request.getEventId(), childProduct.getAsin());
                    if (existingChildGoods != null) {
                        log.warn("子商品已存在，跳过创建: asin={}", childProduct.getAsin());
                        continue;
                    }

                    // 创建子商品
                    Goods childGoods = createGoodsFromProductAndSupply(request.getEventId(), childProduct, supply, store, request.getAmazonSite(), true);
                    goodsMapper.insert(childGoods);
                    resultList.add(convertToDTO(childGoods));

                    log.info("成功创建子商品: id={}, itemSku={}, asin={}",
                            childGoods.getId(), childGoods.getItemSku(), childProduct.getAsin());

                } catch (Exception e) {
                    log.error("创建子商品失败: asin={}, error={}", childProduct.getAsin(), e.getMessage(), e);
                    // 继续处理其他子商品，不中断整个流程
                }
            }

            log.info("完成变体商品创建，共创建 {} 个商品（包含主商品）", resultList.size());
        }

        return resultList;
    }

    @Override
    public IPage<GoodsDTO> getGoodsPage(GoodsQueryParam param) {
        log.info("分页查询商品列表: param={}", param);

        // 创建分页对象
        Page<Goods> page = new Page<>(param.getPage(), param.getSize());

        // 构建查询条件
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();

        // 添加事件ID过滤条件
        if (param.getEventId() != null) {
            queryWrapper.eq(Goods::getEventId, param.getEventId());
        }

        // 添加Amazon ASIN过滤条件
        if (StringUtils.hasText(param.getAmazonAsin())) {
            queryWrapper.eq(Goods::getAmazonAsin, param.getAmazonAsin());
        }

        // 添加状态过滤条件
        if (param.getStatus() != null) {
            queryWrapper.eq(Goods::getStatus, param.getStatus());
        }

        // 添加卖家SKU过滤条件
        if (StringUtils.hasText(param.getItemSku())) {
            queryWrapper.like(Goods::getItemSku, param.getItemSku());
        }

        // 按ID倒序排序
        queryWrapper.orderByDesc(Goods::getId);

        // 执行查询
        IPage<Goods> goodsPage = goodsMapper.selectPage(page, queryWrapper);

        // 转换为DTO对象
        IPage<GoodsDTO> dtoPage = goodsPage.convert(this::convertToDTO);

        return dtoPage;
    }

    @Override
    public GoodsDTO getGoodsDTOById(Long id) {
        log.info("根据ID获取商品详情: id={}", id);

        Goods goods = goodsMapper.selectById(id);
        if (goods == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "商品不存在");
        }

        return convertToDTO(goods);
    }

    @Override
    public List<Goods> getGoodsByEventId(Long eventId) {
        return goodsMapper.findByEventId(eventId);
    }

    @Override
    public List<Goods> getGoodsByAmazonAsin(String amazonAsin) {
        return goodsMapper.findByAmazonAsin(amazonAsin);
    }

    @Override
    public GoodsDTO getGoodsByItemSku(String itemSku) {
        log.info("根据卖家SKU获取商品: itemSku={}", itemSku);

        Goods goods = goodsMapper.findByItemSku(itemSku);
        if (goods == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "商品不存在");
        }

        return convertToDTO(goods);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGoodsStatus(Long id, Integer status, String reviewNotes, Long reviewedBy) {
        log.info("更新商品状态: id={}, status={}, reviewedBy={}", id, status, reviewedBy);

        Goods goods = goodsMapper.selectById(id);
        if (goods == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "商品不存在");
        }

        goods.setStatus(status);
        goods.setReviewNotes(reviewNotes);
        goods.setReviewedBy(reviewedBy);
        goods.setReviewedAt(LocalDateTime.now());
        goods.setUpdatedAt(LocalDateTime.now());

        int result = goodsMapper.updateById(goods);
        return result > 0;
    }

    /**
     * 根据ASIN获取Product信息
     */
    private Product getProductByAsin(String asin) {
        try {
            return productService.getProductDTOByAsin(asin) != null ?
                   productService.getProductById(productService.getProductDTOByAsin(asin).getId()) : null;
        } catch (Exception e) {
            log.warn("获取Product信息失败: asin={}, error={}", asin, e.getMessage());
            return null;
        }
    }

    /**
     * 根据object_id获取Supply信息
     */
    private Supply getSupplyByObjectId(String objectId) {
        try {
            return supplyService.getSupplyDTOByObjectId(objectId) != null ?
                   supplyService.getSupplyById(supplyService.getSupplyDTOByObjectId(objectId).getId()) : null;
        } catch (Exception e) {
            log.warn("获取Supply信息失败: objectId={}, error={}", objectId, e.getMessage());
            return null;
        }
    }

    /**
     * 根据object_id和amazon_asin获取Supply信息
     */
    private Supply getSupplyByObjectIdAndAsin(String objectId, String amazonAsin) {
        try {
            // 直接使用MyBatis-Plus查询，避免重复数据问题
            LambdaQueryWrapper<Supply> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Supply::getObjectId, objectId)
                       .eq(Supply::getAmazonAsin, amazonAsin);

            // 使用SupplyMapper直接查询
            return supplyMapper.selectOne(queryWrapper);

        } catch (Exception e) {
            log.warn("获取Supply信息失败: objectId={}, amazonAsin={}, error={}", objectId, amazonAsin, e.getMessage());
            return null;
        }
    }

    /**
     * 根据ID获取Store信息
     */
    private Store getStoreById(Long storeId) {
        try {
            return storeService.getStoreEntityById(storeId);
        } catch (Exception e) {
            log.warn("获取Store信息失败: storeId={}, error={}", storeId, e.getMessage());
            return null;
        }
    }

    /**
     * 从Product、Supply和Store创建Goods对象
     */
    private Goods createGoodsFromProductAndSupply(Long eventId, Product product, Supply supply, Store store, AmazonSite amazonSite, boolean isChildProduct) {
        Goods goods = new Goods();
        LocalDateTime now = LocalDateTime.now();

        // 基本信息
        goods.setEventId(eventId);
        goods.setAmazonAsin(product.getAsin());
        goods.setSupplyId(supply.getId());

        // 产品类型和基本信息
        goods.setFeedProductType(StringUtils.hasText(product.getProductType()) ? product.getProductType() : "home");
        goods.setItemSku(generateItemSku(product, isChildProduct));

        // 根据AmazonSite所属区域从店铺获取对应品牌名称
        String brandName = getBrandNameByRegion(store, amazonSite);
        goods.setBrandName(brandName);
        goods.setManufacturer(brandName); // 制造商也使用相同的品牌名称


        goods.setItemName(product.getTitle());
        goods.setRecommendedBrowseNodes(product.getRecommendedBrowseNodes());
        goods.setMaxOrderQuantity(100);
        goods.setCountryOfOrigin("CN");

        // 根据AmazonSite设置货币
        goods.setCurrency(amazonSite.getCurrencyCode());

        // 根据AmazonSite的汇率计算价格
        setPriceInfoWithExchangeRate(goods, product, supply, amazonSite);

        // 库存信息
        goods.setQuantity(20); // 默认库存

        // 图片信息
        setImageInfo(goods, product, supply);

        // 变体信息
        setVariantInfo(goods, product,isChildProduct,store);

        // 描述信息
        goods.setUpdateDelete("Update");
        goods.setProductDescription(product.getDescription());
        goods.setPartNumber(generateItemSku(product, false)); //使用主商品的sku

        // 产品特性
        setBulletPoints(goods, product);

        // 产品属性
        goods.setMetalType("other");
        goods.setListPriceWithTax(new BigDecimal("1"));
        goods.setListPrice(new BigDecimal("0"));
        goods.setFulfillmentCenterId("DEFAULT");
        goods.setUnitCountType("Gram");
        goods.setUnitCount(1);
        // 重量信息 - 从product获取OpenAI提取的数据
        goods.setWebsiteShippingWeight(product.getShippingWeight());
        goods.setWebsiteShippingWeightUnitOfMeasure(product.getShippingWeightUnit());


        // 履行和价格
        goods.setConditionType("New");

        // 状态信息
        goods.setStatus(0); // 待审核
        goods.setSourceType(1); // 自动生成
        goods.setGenerationRules("基于Amazon商品(" + product.getAsin() + ")、1688货源(" + supply.getObjectId() +
                ")和店铺(" + store.getStoreName() + ")针对" + amazonSite.getCountryName() + "站点自动生成");

        // 时间戳
        goods.setCreatedAt(now);
        goods.setUpdatedAt(now);

        return goods;
    }

    /**
     * 生成商品SKU
     *
     * @param product 商品信息
     * @param isChildProduct 是否为子商品
     * @return 生成的SKU
     */
    private String generateItemSku(Product product, boolean isChildProduct) {
        if (isChildProduct) {
            // 子商品直接返回product_sku
            return product.getProductSku();
        } else {
            // 主商品使用压缩的ASIN
            return AsinCompressor.compress(product.getAsin());
        }
    }

    /**
     * 从标题中提取品牌名称
     */
    private String extractBrandFromTitle(String title) {
        if (!StringUtils.hasText(title)) {
            return "Generic";
        }

        // 简单的品牌提取逻辑：取第一个单词
        String[] words = title.split("\\s+");
        if (words.length > 0) {
            return words[0].replaceAll("[^a-zA-Z0-9]", "");
        }

        return "Generic";
    }

    /**
     * 根据Amazon站点区域从店铺获取对应品牌名称
     */
    private String getBrandNameByRegion(Store store, AmazonSite amazonSite) {
        SiteRegion siteRegion = amazonSite.getSiteRegion();

        switch (siteRegion) {
            case NORTH_AMERICA:
                // 北美站使用北美站品牌
                if (StringUtils.hasText(store.getNorthAmericaBrand())) {
                    return store.getNorthAmericaBrand();
                }
                break;
            case EUROPE:
                // 欧洲站使用欧洲站品牌
                if (StringUtils.hasText(store.getEuropeBrand())) {
                    return store.getEuropeBrand();
                }
                break;
            case ASIA_PACIFIC:
                // 亚太站优先使用北美站品牌，如果没有则使用欧洲站品牌
                if (StringUtils.hasText(store.getNorthAmericaBrand())) {
                    return store.getNorthAmericaBrand();
                } else if (StringUtils.hasText(store.getEuropeBrand())) {
                    return store.getEuropeBrand();
                }
                break;
        }

        // 如果都没有配置，使用店铺名称作为品牌
        if (StringUtils.hasText(store.getStoreName())) {
            return store.getStoreName();
        }

        // 最后的默认值
        return "Generic";
    }



    /**
     * 根据新的定价公式设置价格信息
     * 公式：售价 = (X*(1+商品售卖利润))/(1-平台抽佣比例)/目标国家汇率
     * 其中 X = 货源成本 + 运输成本
     */
    private void setPriceInfoWithExchangeRate(Goods goods, Product product, Supply supply, AmazonSite amazonSite) {
        // 获取当前汇率
        BigDecimal exchangeRate = amazonSiteConfig.getCurrentExchangeRate(amazonSite.getCountryCode());

        log.info("开始计算价格: 站点={}, 货币={}, 汇率={}",
                amazonSite.getCountryName(), amazonSite.getCurrencyCode(), exchangeRate);

        // 1. 货源成本（1688价格，人民币）
        BigDecimal supplyCostRMB = getSupplyCost(supply);
        if (supplyCostRMB == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,
                    "无法获取1688货源价格，无法计算商品定价。请确保货源数据包含有效价格信息。");
        }

        // 2. 计算运输成本
        BigDecimal shippingCost = calculateShippingCost(product);

        // 3. 总成本 X = 货源成本 + 运输成本
        BigDecimal totalCostRMB = supplyCostRMB.add(shippingCost);

        // 4. 应用定价公式
        // 商品售卖利润 = 0.8 (80%)
        // 平台抽佣比例 = 0.15 (15%)
        BigDecimal profitRate = new BigDecimal("0.8");
        BigDecimal commissionRate = new BigDecimal("0.15");

        // 售价(人民币) = X * (1 + 利润率) / (1 - 抽佣率)
        BigDecimal sellingPriceRMB = totalCostRMB
            .multiply(BigDecimal.ONE.add(profitRate))
            .divide(BigDecimal.ONE.subtract(commissionRate), 4, RoundingMode.HALF_UP);

        // 5. 转换为目标货币
        BigDecimal standardPrice = sellingPriceRMB.divide(exchangeRate, 4, RoundingMode.HALF_UP);

        // 6. 设置价格（保留2位小数）
        goods.setStandardPrice(standardPrice.setScale(2, RoundingMode.HALF_UP));
        goods.setListPrice(standardPrice.multiply(new BigDecimal("1.2")).setScale(2, RoundingMode.HALF_UP));

        log.info("新定价公式计算完成: 货源成本={}元, 运输成本={}元, 总成本={}元, 售价(RMB)={}元, 售价({})={}",
                supplyCostRMB, shippingCost, totalCostRMB, sellingPriceRMB,
                amazonSite.getCurrencyCode(), standardPrice);
    }

    /**
     * 获取1688货源成本
     */
    private BigDecimal getSupplyCost(Supply supply) {
        if (!StringUtils.hasText(supply.getPrice())) {
            return null;
        }

        try {
            String priceStr = supply.getPrice().replaceAll("[^0-9.]", "");
            return new BigDecimal(priceStr);
        } catch (Exception e) {
            log.warn("解析1688价格失败: price={}, error={}", supply.getPrice(), e.getMessage());
            return null;
        }
    }

    /**
     * 计算运输成本
     */
    private BigDecimal calculateShippingCost(Product product) {
        // 1. 实际重量 (KG)
        BigDecimal actualWeight = getActualWeight(product);

        // 2. 计抛重量 = (长×宽×高)/6000 (CM)
        BigDecimal volumetricWeight = calculateVolumetricWeight(product);

        // 3. 运输重量 = (计抛重量+实际重量)/2
        BigDecimal shippingWeight = actualWeight.add(volumetricWeight)
            .divide(new BigDecimal("2"), 4, RoundingMode.HALF_UP);

        // 4. 运输成本 = 运输重量 × 100
        BigDecimal shippingCost = shippingWeight.multiply(new BigDecimal("100"));

        log.info("运输成本计算: 实际重量={}KG, 计抛重量={}KG, 运输重量={}KG, 运输成本={}元",
                actualWeight, volumetricWeight, shippingWeight, shippingCost);

        return shippingCost;
    }

    /**
     * 获取实际重量 (KG)
     */
    private BigDecimal getActualWeight(Product product) {
        if (product.getShippingWeight() != null && product.getShippingWeight().compareTo(BigDecimal.ZERO) > 0) {
            return product.getShippingWeight();
        }

        // 如果没有重量信息，使用默认值 0.5KG
        log.warn("商品缺少重量信息，使用默认值0.5KG: asin={}", product.getAsin());
        return new BigDecimal("0.5");
    }

    /**
     * 计算计抛重量 (长×宽×高)/6000
     */
    private BigDecimal calculateVolumetricWeight(Product product) {
        BigDecimal length = product.getItemDepthFrontToBack();
        BigDecimal width = product.getItemWidthSideToSide();
        BigDecimal height = product.getItemHeightFloorToTop();

        if (length != null && width != null && height != null &&
            length.compareTo(BigDecimal.ZERO) > 0 &&
            width.compareTo(BigDecimal.ZERO) > 0 &&
            height.compareTo(BigDecimal.ZERO) > 0) {

            // 计抛重量 = (长×宽×高)/6000
            BigDecimal volume = length.multiply(width).multiply(height);
            return volume.divide(new BigDecimal("6000"), 4, RoundingMode.HALF_UP);
        }

        // 如果没有尺寸信息，使用默认计抛重量 0.3KG
        log.warn("商品缺少尺寸信息，使用默认计抛重量0.3KG: asin={}", product.getAsin());
        return new BigDecimal("0.3");
    }



    /**
     * 设置图片信息
     */
    private void setImageInfo(Goods goods, Product product, Supply supply) {
        try {
            // 优先使用Amazon商品图片
            if (StringUtils.hasText(product.getImages())) {
                List<String> imageUrls = objectMapper.readValue(product.getImages(),
                        new TypeReference<List<String>>() {});

                if (!imageUrls.isEmpty()) {
                    goods.setMainImageUrl(imageUrls.get(0));

                    // 设置其他图片
                    for (int i = 1; i < Math.min(imageUrls.size(), 9); i++) {
                        setOtherImageUrl(goods, i, imageUrls.get(i));
                    }
                }
            }

            // 如果没有Amazon图片，使用1688图片
            if (!StringUtils.hasText(goods.getMainImageUrl()) && StringUtils.hasText(supply.getImageUrl())) {
                goods.setMainImageUrl(supply.getImageUrl());
            }
        } catch (Exception e) {
            log.warn("设置图片信息失败: {}", e.getMessage());
        }
    }

    /**
     * 设置其他图片URL
     */
    private void setOtherImageUrl(Goods goods, int index, String url) {
        switch (index) {
            case 1: goods.setOtherImageUrl1(url); break;
            case 2: goods.setOtherImageUrl2(url); break;
            case 3: goods.setOtherImageUrl3(url); break;
            case 4: goods.setOtherImageUrl4(url); break;
            case 5: goods.setOtherImageUrl5(url); break;
            case 6: goods.setOtherImageUrl6(url); break;
            case 7: goods.setOtherImageUrl7(url); break;
            case 8: goods.setOtherImageUrl8(url); break;
        }
    }

    /**
     * 设置变体信息
     */
    private void setVariantInfo(Goods goods, Product product,boolean isChildProduct, Store store) {
        // 1. 判断是否为变体商品的主商品
        if (product.getIsMainProduct() != null && product.getIsMainProduct() &&
            product.getIsParentVariant() != null && product.getIsParentVariant() &&
            !isChildProduct) {
            goods.setParentChild("parent");

            // 为主商品设置变体主题
            setVariationThemeForParent(goods, product);
        }
        // 2. 判断是否为变体商品的子商品
        else if (isChildProduct) {
            goods.setParentChild("child");
            goods.setParentSku(AsinCompressor.compress(product.getParentAsin()));
            goods.setRelationshipType("Variation");
            // 生成EAN编码并设置为外部产品ID
            String eanCode = generateEanCode(store);
            goods.setExternalProductId(eanCode);
            goods.setExternalProductIdType("EAN");

            // 解析变体属性并设置变体主题和属性
            parseAndSetVariantAttributes(goods, product);
        }else{
            // 生成EAN编码并设置为外部产品ID
            String eanCode = generateEanCode(store);
            goods.setExternalProductId(eanCode);
            goods.setExternalProductIdType("EAN");
        }
    }

    /**
     * 为主商品设置变体主题
     */
    private void setVariationThemeForParent(Goods goods, Product product) {
        if (!StringUtils.hasText(product.getVariantAttributes())) {
            return;
        }

        try {
            // 解析JSON格式的变体属性
            Map<String, Object> variantAttrs = objectMapper.readValue(
                product.getVariantAttributes(),
                new TypeReference<Map<String, Object>>() {}
            );

            boolean hasColorName = variantAttrs.containsKey("color_name");
            boolean hasSizeName = variantAttrs.containsKey("size_name");

            if (hasColorName && hasSizeName) {
                // 同时包含颜色和尺寸
                goods.setVariationTheme("ColorSize");
                log.info("主商品设置变体主题为ColorSize: productId={}", product.getId());

            } else if (hasSizeName) {
                // 只包含尺寸
                goods.setVariationTheme("Size");
                log.info("主商品设置变体主题为Size: productId={}", product.getId());

            } else if (hasColorName) {
                // 只包含颜色
                goods.setVariationTheme("Color");
                log.info("主商品设置变体主题为Color: productId={}", product.getId());
            }

        } catch (Exception e) {
            log.warn("主商品解析变体属性失败: productId={}, variantAttributes={}, error={}",
                    product.getId(), product.getVariantAttributes(), e.getMessage());
        }
    }

    /**
     * 解析并设置变体属性
     */
    private void parseAndSetVariantAttributes(Goods goods, Product product) {
        if (!StringUtils.hasText(product.getVariantAttributes())) {
            return;
        }

        try {
            // 解析JSON格式的变体属性
            Map<String, Object> variantAttrs = objectMapper.readValue(
                product.getVariantAttributes(),
                new TypeReference<Map<String, Object>>() {}
            );

            boolean hasColorName = variantAttrs.containsKey("color_name");
            boolean hasSizeName = variantAttrs.containsKey("size_name");

            if (hasColorName && hasSizeName) {
                // 同时包含颜色和尺寸
                goods.setVariationTheme("ColorSize");

                String sizeValue = String.valueOf(variantAttrs.get("size_name"));
                goods.setSizeName(sizeValue);
                goods.setSizeMap(sizeValue);

                String colorValue = String.valueOf(variantAttrs.get("color_name"));
                goods.setColorName(colorValue);
                goods.setColorMap(colorValue);

                log.info("设置变体主题为ColorSize: sizeName={}, colorName={}", sizeValue, colorValue);

            } else if (hasSizeName) {
                // 只包含尺寸
                goods.setVariationTheme("Size");

                String sizeValue = String.valueOf(variantAttrs.get("size_name"));
                goods.setSizeName(sizeValue);
                goods.setSizeMap(sizeValue);

                log.info("设置变体主题为Size: sizeName={}", sizeValue);

            } else if (hasColorName) {
                // 只包含颜色
                goods.setVariationTheme("Color");

                String colorValue = String.valueOf(variantAttrs.get("color_name"));
                goods.setColorName(colorValue);
                goods.setColorMap(colorValue);

                log.info("设置变体主题为Color: colorName={}", colorValue);
            }

        } catch (Exception e) {
            log.warn("解析变体属性失败: productId={}, variantAttributes={}, error={}",
                    product.getId(), product.getVariantAttributes(), e.getMessage());
        }
    }

    /**
     * 设置产品特性
     */
    private void setBulletPoints(Goods goods, Product product) {
        if (StringUtils.hasText(product.getFeature())) {
            String[] features = product.getFeature().split("\n");
            for (int i = 0; i < Math.min(features.length, 5); i++) {
                String feature = features[i].trim();
                if (StringUtils.hasText(feature)) {
                    setBulletPoint(goods, i + 1, feature);
                }
            }
        }
    }

    /**
     * 设置单个产品特性
     */
    private void setBulletPoint(Goods goods, int index, String feature) {
        switch (index) {
            case 1: goods.setBulletPoint1(feature); break;
            case 2: goods.setBulletPoint2(feature); break;
            case 3: goods.setBulletPoint3(feature); break;
            case 4: goods.setBulletPoint4(feature); break;
            case 5: goods.setBulletPoint5(feature); break;
        }
    }

    /**
     * 生成EAN编码
     */
    private String generateEanCode(Store store) {
        try {
            // 检查店铺是否配置了EAN信息
            if (!isEanConfigValid(store)) {
                log.warn("店铺EAN配置不完整，跳过EAN编码生成: storeId={}", store.getId());
                return null;
            }

            // 获取下一个产品代码
            String nextProductCode = storeService.getNextEanProductCode(store.getId());

            // 格式化产品代码为5位
            String formattedProductCode = EanCodeUtil.formatNumber(Integer.parseInt(nextProductCode), 5);

            // 生成EAN-13编码
            String eanCode = EanCodeUtil.generateEAN13(
                store.getEanCountryCode(),
                store.getEanManufacturerCode(),
                formattedProductCode
            );

            if (eanCode != null) {
                log.info("成功生成EAN编码: eanCode={}, storeId={}, productCode={}",
                        eanCode, store.getId(), formattedProductCode);
                return eanCode;
            } else {
                log.warn("EAN编码生成失败: storeId={}, countryCode={}, manufacturerCode={}, productCode={}",
                        store.getId(), store.getEanCountryCode(), store.getEanManufacturerCode(), formattedProductCode);
                return null;
            }

        } catch (Exception e) {
            log.error("生成EAN编码时发生异常: storeId={}, error={}", store.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证店铺EAN配置是否有效
     */
    private boolean isEanConfigValid(Store store) {
        return StringUtils.hasText(store.getEanCountryCode()) &&
               StringUtils.hasText(store.getEanManufacturerCode()) &&
               StringUtils.hasText(store.getEanProductCodeCurrent());
    }

    /**
     * 转换为DTO对象
     */
    private GoodsDTO convertToDTO(Goods goods) {
        GoodsDTO dto = new GoodsDTO();
        BeanUtils.copyProperties(goods, dto);
        return dto;
    }
}
